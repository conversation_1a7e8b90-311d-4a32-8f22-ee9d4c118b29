import asyncio
import os
from autogen_agentchat.agents import Assistant<PERSON>gent
from autogen_agentchat.teams import SelectorGroupChat
from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_agentchat.ui import Console

async def main():
    """Create and run the AI development team programmatically"""
    
    # Set up environment variables
    os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"
    
    print("✅ OpenAI API key configured successfully")
    
    try:
        # Create model client
        model_client = OpenAIChatCompletionClient(
            model="gpt-4o",
            temperature=0.3
        )
        
        # Create agents
        ai_architect = AssistantAgent(
            name="ai_architect",
            model_client=model_client,
            system_message="""You are an AI Architecture Specialist with expertise in designing scalable AI applications. Your responsibilities include:
1. Design overall system architecture for AI applications
2. Plan integration patterns for FastAPI, RAG, vector databases, and external services
3. Define data flow and component interactions
4. Ensure scalability, security, and performance considerations
5. Create technical specifications and architecture diagrams
6. Guide technology stack decisions
7. Plan deployment and infrastructure requirements
Focus on microservices architecture, cloud-native solutions, and best practices for AI/ML applications."""
        )
        
        fastapi_developer = AssistantAgent(
            name="fastapi_developer",
            model_client=OpenAIChatCompletionClient(model="gpt-4o", temperature=0.2),
            system_message="""You are a FastAPI Backend Development Specialist. Your expertise includes:
1. Building high-performance FastAPI applications
2. Implementing RESTful APIs with proper documentation
3. Database integration with Supabase and PostgreSQL
4. Authentication and authorization (JWT, OAuth2)
5. Middleware implementation and request/response handling
6. API versioning and rate limiting
7. WebSocket implementation for real-time features
8. Integration with external services and APIs
9. Performance optimization and caching strategies
Focus on clean, maintainable code following FastAPI best practices."""
        )
        
        supabase_specialist = AssistantAgent(
            name="supabase_specialist",
            model_client=OpenAIChatCompletionClient(model="gpt-4o", temperature=0.2),
            system_message="""You are a Supabase and Database Specialist. Your expertise includes:
1. Supabase setup, configuration, and optimization
2. PostgreSQL database design and performance tuning
3. pgvector extension for vector storage and similarity search
4. Row Level Security (RLS) and authentication policies
5. Real-time subscriptions and database triggers
6. Supabase Auth integration and user management
7. Edge Functions and serverless database operations
8. Database migrations and schema management
9. Backup, recovery, and monitoring strategies
10. Integration with Python applications using supabase-py
Focus on scalable, secure database architectures optimized for AI applications."""
        )
        
        rag_ai_specialist = AssistantAgent(
            name="rag_ai_specialist",
            model_client=OpenAIChatCompletionClient(model="gpt-4o", temperature=0.2),
            system_message="""You are a RAG (Retrieval-Augmented Generation) and AI Integration Specialist. Your expertise includes:
1. Designing and implementing RAG pipelines for intelligent document retrieval
2. LLM integration with OpenAI, Anthropic, and other AI models
3. Embedding strategies and semantic search optimization
4. Context management and prompt engineering
5. AI model fine-tuning and optimization
6. Knowledge base construction and maintenance
7. Intelligent FAQ systems and chatbot development
8. AI-powered content generation and summarization
9. Multi-modal AI integration (text, image, audio)
10. AI model evaluation and performance monitoring
Focus on creating intelligent, context-aware AI systems that provide accurate and relevant responses."""
        )
        
        # Create termination condition
        termination = MaxMessageTermination(max_messages=50)
        
        # Create team
        team = SelectorGroupChat(
            participants=[ai_architect, fastapi_developer, supabase_specialist, rag_ai_specialist],
            model_client=model_client,
            termination_condition=termination
        )
        
        # Define the task
        task = """
        Build an AI Enabled Prospect Outreach System (Phase 1) with the following requirements:

        **PROJECT OVERVIEW:**
        - Purpose: AI-enabled prospect outreach system to identify lead intent and provide prequalified leads
        - Communication: SMS-based communication only (Phase 1)
        - Target Region: Australia, Currency: AUD, Language: English only
        - Platform: Web-based admin panel with React JS frontend

        **CORE SYSTEM REQUIREMENTS:**
        1. **User Management & Authentication**
           - Login with Email/Mobile, Remember Me, Forgot Password
           - User Role: Master Admin/System Owner

        2. **Franchise Management**
           - CRUD operations for franchises
           - Fields: Name, Category, Region, Budget, Sub Category
           - Upload franchise brochures, Zoho CRM integration, CSV import

        3. **Document Management System**
           - PDF handling with vector storage
           - RAG implementation for document analysis
           - Map franchises with documents

        4. **Sales Script Management**
           - CRUD operations for scripts
           - Map scripts with specific franchises

        5. **Lead Management System**
           - Import via CSV, Zoho CRM sync, manual entry
           - Display leads with communication history and search

        6. **Question Bank & Prequalification**
           - CRUD operations for questions
           - Mark questions as completed, virtual agent exception handling

        7. **General Settings & Configuration**
           - System messages, holiday messages, agent exceptions

        8. **Meeting Scheduling Integration**
           - Calendly integration with OAuth
           - Automatic booking based on availability

        9. **Analytics & Reporting**
           - SMS count per day, escalation tracking, communication history

        **TECHNICAL INTEGRATIONS:**
        - **Zoho CRM**: Fetch leads, push qualification data and SMS history
        - **SMS Service**: Twilio/Telstra integration with automated sending
        - **AI/RAG**: OpenAI GPT with RAG for document analysis and dynamic responses
        - **Calendar**: Calendly integration for meeting scheduling

        **TECHNICAL STACK:**
        - Frontend: React JS with AdminLTE theme
        - Backend: FastAPI (Python preferred)
        - Database: Vector database (Pinecone/Weaviate) + PostgreSQL
        - Infrastructure: AWS deployment

        **CONSTRAINTS:**
        - Phase 1: SMS only, no voice communication
        - Portrait mode only, no tablet support
        - Manual and unit testing required
        - 5-7 weeks timeline, 306 hours effort

        Please start by designing the overall system architecture, then proceed with implementation focusing on scalable, modular structure with proper error handling and API documentation.
        """
        
        # Run the team
        print("🚀 Starting AI Development Team...")
        print("=" * 60)
        
        stream = team.run_stream(task=task)
        await Console(stream)
        
        print("=" * 60)
        print("✅ Team execution completed!")
        
    except Exception as e:
        print(f"❌ Error running team: {e}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
