{
  "provider": "autogen_agentchat.teams.SelectorGroupChat",
  "component_type": "team",
  "version": 1,
  "component_version": 1,
  "description": "A comprehensive team of 8 specialized agents for building Python AI applications with FastAPI, RAG, vector analysis, SMS integration, and Supabase backend - including architecture design, development, testing, and deployment specialists.",
  "label": "AI_Application_Development_Team",
  "config": {
    "participants": [
      {
        "provider": "autogen_agentchat.agents.AssistantAgent",
        "component_type": "agent",
        "version": 1,
        "component_version": 1,
        "description": "Lead architect specializing in AI application design and system architecture",
        "label": "AI_Architect",
        "config": {
          "name": "ai_architect",
          "model_client": {
            "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
            "component_type": "model",
            "version": 1,
            "component_version": 1,
            "description": "Chat completion client for OpenAI hosted models.",
            "label": "OpenAIChatCompletionClient",
            "config": {
              "temperature": 0.3,
              "model": "gpt-4o"
            }
          },
          "workbench": {
            "provider": "autogen_core.tools.StaticWorkbench",
            "component_type": "workbench",
            "version": 1,
            "component_version": 1,
            "description": "A workbench that provides a static set of tools that do not change after each tool execution.",
            "label": "StaticWorkbench",
            "config": {
              "tools": [
                {
                  "provider": "autogen_core.tools.FunctionTool",
                  "component_type": "tool",
                  "version": 1,
                  "component_version": 1,
                  "description": "Create architecture diagrams and system designs",
                  "label": "ArchitectureTool",
                  "config": {
                    "source_code": "async def create_architecture_diagram(components: List[str], connections: List[Dict[str, str]], output_format: str = 'mermaid') -> str:\n    \"\"\"Create system architecture diagrams\n    \n    Args:\n        components: List of system components\n        connections: List of connections between components\n        output_format: Output format (mermaid, plantuml)\n    \n    Returns:\n        str: Architecture diagram in specified format\n    \"\"\"\n    if output_format == 'mermaid':\n        diagram = 'graph TD\\n'\n        for component in components:\n            diagram += f'    {component.replace(\" \", \"_\")}[\"{component}\"]\\n'\n        for conn in connections:\n            source = conn['from'].replace(' ', '_')\n            target = conn['to'].replace(' ', '_')\n            label = conn.get('label', '')\n            diagram += f'    {source} --> {target}'\n            if label:\n                diagram += f' : {label}'\n            diagram += '\\n'\n        return diagram\n    return 'Architecture diagram created'",
                    "name": "create_architecture_diagram",
                    "description": "Create system architecture diagrams for AI applications",
                    "global_imports": [
                      {
                        "module": "typing",
                        "imports": [
                          "List",
                          "Dict"
                        ]
                      }
                    ],
                    "has_cancellation_support": false
                  }
                }
              ]
            }
          },
          "model_context": {
            "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
            "component_type": "chat_completion_context",
            "version": 1,
            "component_version": 1,
            "description": "An unbounded chat completion context that keeps a view of all messages.",
            "label": "UnboundedChatCompletionContext",
            "config": {}
          },
          "description": "AI Architecture Specialist - designs scalable AI application architectures",
          "system_message": "You are an AI Architecture Specialist with expertise in designing scalable AI applications. Your responsibilities include:\n1. Design overall system architecture for AI applications\n2. Plan integration patterns for FastAPI, RAG, vector databases, and external services\n3. Define data flow and component interactions\n4. Ensure scalability, security, and performance considerations\n5. Create technical specifications and architecture diagrams\n6. Guide technology stack decisions\n7. Plan deployment and infrastructure requirements\nFocus on microservices architecture, cloud-native solutions, and best practices for AI/ML applications.",
          "model_client_stream": false,
          "reflect_on_tool_use": false,
          "tool_call_summary_format": "{result}",
          "metadata": {}
        }
      },
      {
        "provider": "autogen_agentchat.agents.AssistantAgent",
        "component_type": "agent",
        "version": 1,
        "component_version": 1,
        "description": "FastAPI and backend development specialist",
        "label": "FastAPI_Developer",
        "config": {
          "name": "fastapi_developer",
          "model_client": {
            "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
            "component_type": "model",
            "version": 1,
            "component_version": 1,
            "description": "Chat completion client for OpenAI hosted models.",
            "label": "OpenAIChatCompletionClient",
            "config": {
              "temperature": 0.2,
              "model": "gpt-4o"
            }
          },
          "workbench": {
            "provider": "autogen_core.tools.StaticWorkbench",
            "component_type": "workbench",
            "version": 1,
            "component_version": 1,
            "description": "A workbench for FastAPI development tools",
            "label": "StaticWorkbench",
            "config": {
              "tools": [
                {
                  "provider": "autogen_core.tools.FunctionTool",
                  "component_type": "tool",
                  "version": 1,
                  "component_version": 1,
                  "description": "Generate FastAPI code templates",
                  "label": "FastAPIGenerator",
                  "config": {
                    "source_code": "async def generate_fastapi_template(endpoints: List[Dict[str, str]], auth_type: str = 'jwt') -> str:\n    \"\"\"Generate FastAPI application template\n    \n    Args:\n        endpoints: List of API endpoints with methods and descriptions\n        auth_type: Authentication type (jwt, oauth2, api_key)\n    \n    Returns:\n        str: FastAPI application template code\n    \"\"\"\n    template = '''from fastapi import FastAPI, Depends, HTTPException, status\nfrom fastapi.security import HTTPBearer\nfrom fastapi.middleware.cors import CORSMiddleware\nfrom pydantic import BaseModel\nimport uvicorn\n\napp = FastAPI(title=\"AI Application API\", version=\"1.0.0\")\n\n# CORS middleware\napp.add_middleware(\n    CORSMiddleware,\n    allow_origins=[\"*\"],\n    allow_credentials=True,\n    allow_methods=[\"*\"],\n    allow_headers=[\"*\"],\n)\n\n# Security\nsecurity = HTTPBearer()\n\n'''\n    \n    for endpoint in endpoints:\n        method = endpoint.get('method', 'GET').lower()\n        path = endpoint.get('path', '/')\n        description = endpoint.get('description', '')\n        \n        template += f'''\n@app.{method}(\"{path}\")\nasync def {path.replace('/', '_').replace('-', '_').strip('_')}():\n    \"\"\"{description}\"\"\"\n    return {{\"message\": \"Endpoint implementation needed\"}}\n'''\n    \n    template += '''\nif __name__ == \"__main__\":\n    uvicorn.run(app, host=\"0.0.0.0\", port=8000)'''\n    \n    return template",
                    "name": "generate_fastapi_template",
                    "description": "Generate FastAPI application templates with endpoints",
                    "global_imports": [
                      {
                        "module": "typing",
                        "imports": [
                          "List",
                          "Dict"
                        ]
                      }
                    ],
                    "has_cancellation_support": false
                  }
                }
              ]
            }
          },
          "model_context": {
            "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
            "component_type": "chat_completion_context",
            "version": 1,
            "component_version": 1,
            "description": "An unbounded chat completion context that keeps a view of all messages.",
            "label": "UnboundedChatCompletionContext",
            "config": {}
          },
          "description": "FastAPI Backend Development Specialist",
          "system_message": "You are a FastAPI Backend Development Specialist. Your expertise includes:\n1. Building high-performance FastAPI applications\n2. Implementing RESTful APIs with proper documentation\n3. Database integration with Supabase and PostgreSQL\n4. Authentication and authorization (JWT, OAuth2)\n5. Middleware implementation and request/response handling\n6. API versioning and rate limiting\n7. WebSocket implementation for real-time features\n8. Integration with external services and APIs\n9. Performance optimization and caching strategies\nFocus on clean, maintainable code following FastAPI best practices.",
          "model_client_stream": false,
          "reflect_on_tool_use": false,
          "tool_call_summary_format": "{result}",
          "metadata": {}
        }
      },
      {
        "provider": "autogen_agentchat.agents.AssistantAgent",
        "component_type": "agent",
        "version": 1,
        "component_version": 1,
        "description": "RAG and AI integration specialist",
        "label": "RAG_AI_Specialist",
        "config": {
          "name": "rag_ai_specialist",
          "model_client": {
            "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
            "component_type": "model",
            "version": 1,
            "component_version": 1,
            "description": "Chat completion client for OpenAI hosted models.",
            "label": "OpenAIChatCompletionClient",
            "config": {
              "temperature": 0.1,
              "model": "gpt-4o"
            }
          },
          "workbench": {
            "provider": "autogen_core.tools.StaticWorkbench",
            "component_type": "workbench",
            "version": 1,
            "component_version": 1,
            "description": "A workbench for RAG and AI tools",
            "label": "StaticWorkbench",
            "config": {
              "tools": [
                {
                  "provider": "autogen_core.tools.FunctionTool",
                  "component_type": "tool",
                  "version": 1,
                  "component_version": 1,
                  "description": "Design RAG pipeline architecture",
                  "label": "RAGPipelineDesigner",
                  "config": {
                    "source_code": "async def design_rag_pipeline(document_types: List[str], embedding_model: str = 'sentence-transformers', vector_db: str = 'supabase') -> Dict[str, Any]:\n    \"\"\"Design RAG pipeline architecture\n    \n    Args:\n        document_types: Types of documents to process\n        embedding_model: Embedding model to use\n        vector_db: Vector database choice\n    \n    Returns:\n        Dict: RAG pipeline configuration\n    \"\"\"\n    pipeline_config = {\n        'ingestion': {\n            'document_loaders': [],\n            'text_splitters': [],\n            'preprocessing': []\n        },\n        'embedding': {\n            'model': embedding_model,\n            'dimension': 768 if 'sentence-transformers' in embedding_model else 1536,\n            'batch_size': 32\n        },\n        'vector_store': {\n            'database': vector_db,\n            'index_type': 'ivfflat',\n            'similarity_metric': 'cosine'\n        },\n        'retrieval': {\n            'top_k': 5,\n            'similarity_threshold': 0.7,\n            'reranking': True\n        },\n        'generation': {\n            'model': 'gpt-4o',\n            'temperature': 0.1,\n            'max_tokens': 1000\n        }\n    }\n    \n    # Configure document loaders based on types\n    for doc_type in document_types:\n        if doc_type.lower() in ['pdf']:\n            pipeline_config['ingestion']['document_loaders'].append('PyPDFLoader')\n        elif doc_type.lower() in ['txt', 'text']:\n            pipeline_config['ingestion']['document_loaders'].append('TextLoader')\n        elif doc_type.lower() in ['docx', 'doc']:\n            pipeline_config['ingestion']['document_loaders'].append('DocxLoader')\n        elif doc_type.lower() in ['csv']:\n            pipeline_config['ingestion']['document_loaders'].append('CSVLoader')\n    \n    return pipeline_config",
                    "name": "design_rag_pipeline",
                    "description": "Design RAG pipeline architecture based on requirements",
                    "global_imports": [
                      {
                        "module": "typing",
                        "imports": [
                          "List",
                          "Dict",
                          "Any"
                        ]
                      }
                    ],
                    "has_cancellation_support": false
                  }
                }
              ]
            }
          },
          "model_context": {
            "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
            "component_type": "chat_completion_context",
            "version": 1,
            "component_version": 1,
            "description": "An unbounded chat completion context that keeps a view of all messages.",
            "label": "UnboundedChatCompletionContext",
            "config": {}
          },
          "description": "RAG and AI Integration Specialist",
          "system_message": "You are a RAG (Retrieval-Augmented Generation) and AI Integration Specialist. Your expertise includes:\n1. Designing and implementing RAG pipelines\n2. Document ingestion, chunking, and preprocessing\n3. Embedding generation and vector storage optimization\n4. Retrieval strategies and similarity search\n5. LLM integration (OpenAI, Anthropic, local models)\n6. Prompt engineering and context management\n7. RAG evaluation and performance tuning\n8. Multi-modal RAG for text, images, and structured data\n9. Advanced techniques like hybrid search, re-ranking, and query expansion\nFocus on building production-ready RAG systems with high accuracy and low latency.",
          "model_client_stream": false,
          "reflect_on_tool_use": false,
          "tool_call_summary_format": "{result}",
          "metadata": {}
        }
      },
      {
        "provider": "autogen_agentchat.agents.AssistantAgent",
        "component_type": "agent",
        "version": 1,
        "component_version": 1,
        "description": "Vector database and document analysis expert",
        "label": "Vector_Expert",
        "config": {
          "name": "vector_expert",
          "model_client": {
            "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
            "component_type": "model",
            "version": 1,
            "component_version": 1,
            "description": "Chat completion client for OpenAI hosted models.",
            "label": "OpenAIChatCompletionClient",
            "config": {
              "temperature": 0.1,
              "model": "gpt-4o"
            }
          },
          "workbench": {
            "provider": "autogen_core.tools.StaticWorkbench",
            "component_type": "workbench",
            "version": 1,
            "component_version": 1,
            "description": "A workbench for vector analysis and document processing tools",
            "label": "StaticWorkbench",
            "config": {
              "tools": [
                {
                  "provider": "autogen_core.tools.FunctionTool",
                  "component_type": "tool",
                  "version": 1,
                  "component_version": 1,
                  "description": "Analyze document structure and content",
                  "label": "DocumentAnalyzer",
                  "config": {
                    "source_code": "async def analyze_document_structure(file_path: str, analysis_type: str = 'comprehensive') -> Dict[str, Any]:\n    \"\"\"Analyze document structure and extract metadata\n    \n    Args:\n        file_path: Path to the document file\n        analysis_type: Type of analysis (basic, comprehensive, semantic)\n    \n    Returns:\n        Dict: Document analysis results\n    \"\"\"\n    import os\n    from pathlib import Path\n    \n    file_ext = Path(file_path).suffix.lower()\n    file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0\n    \n    analysis_result = {\n        'file_info': {\n            'name': Path(file_path).name,\n            'extension': file_ext,\n            'size_bytes': file_size,\n            'size_mb': round(file_size / (1024 * 1024), 2)\n        },\n        'content_analysis': {\n            'estimated_pages': max(1, file_size // 2000),  # Rough estimate\n            'processing_strategy': 'text_extraction',\n            'chunking_strategy': 'semantic',\n            'embedding_approach': 'sentence_transformers'\n        },\n        'vector_storage': {\n            'recommended_chunk_size': 1000,\n            'overlap_size': 200,\n            'estimated_vectors': max(1, file_size // 1000),\n            'storage_requirements_mb': max(1, file_size // 1000) * 0.003  # Rough estimate for embeddings\n        },\n        'indexing_strategy': {\n            'index_type': 'ivfflat' if file_size > 10000000 else 'exact',\n            'similarity_metric': 'cosine',\n            'quantization': file_size > 50000000\n        }\n    }\n    \n    if analysis_type == 'comprehensive':\n        analysis_result['optimization'] = {\n            'batch_processing': file_size > 5000000,\n            'parallel_processing': True,\n            'caching_strategy': 'memory' if file_size < 1000000 else 'disk',\n            'preprocessing_steps': ['clean_text', 'normalize', 'deduplicate']\n        }\n    \n    return analysis_result",
                    "name": "analyze_document_structure",
                    "description": "Analyze document structure for optimal vector processing",
                    "global_imports": [
                      {
                        "module": "typing",
                        "imports": [
                          "Dict",
                          "Any"
                        ]
                      }
                    ],
                    "has_cancellation_support": false
                  }
                }
              ]
            }
          },
          "model_context": {
            "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
            "component_type": "chat_completion_context",
            "version": 1,
            "component_version": 1,
            "description": "An unbounded chat completion context that keeps a view of all messages.",
            "label": "UnboundedChatCompletionContext",
            "config": {}
          },
          "description": "Vector Database and Document Analysis Expert",
          "system_message": "You are a Vector Database and Document Analysis Expert. Your specializations include:\n1. Vector database design and optimization (Supabase pgvector, Pinecone, Weaviate)\n2. Document parsing and content extraction from various formats\n3. Text preprocessing and normalization techniques\n4. Embedding strategies and dimensionality optimization\n5. Index creation and query optimization\n6. Similarity search algorithms and performance tuning\n7. Document chunking strategies for optimal retrieval\n8. Vector storage and retrieval scalability\n9. Metadata extraction and structured data handling\n10. Document classification and semantic analysis\nFocus on building efficient, scalable vector systems for document analysis and retrieval.",
          "model_client_stream": false,
          "reflect_on_tool_use": false,
          "tool_call_summary_format": "{result}",
          "metadata": {}
        }
      },
      {
        "provider": "autogen_agentchat.agents.AssistantAgent",
        "component_type": "agent",
        "version": 1,
        "component_version": 1,
        "description": "SMS integration and external services specialist",
        "label": "SMS_Integration_Specialist",
        "config": {
          "name": "sms_integration_specialist",
          "model_client": {
            "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
            "component_type": "model",
            "version": 1,
            "component_version": 1,
            "description": "Chat completion client for OpenAI hosted models.",
            "label": "OpenAIChatCompletionClient",
            "config": {
              "temperature": 0.2,
              "model": "gpt-4o"
            }
          },
          "workbench": {
            "provider": "autogen_core.tools.StaticWorkbench",
            "component_type": "workbench",
            "version": 1,
            "component_version": 1,
            "description": "A workbench for SMS and external service integration tools",
            "label": "StaticWorkbench",
            "config": {
              "tools": [
                {
                  "provider": "autogen_core.tools.FunctionTool",
                  "component_type": "tool",
                  "version": 1,
                  "component_version": 1,
                  "description": "Generate SMS integration code",
                  "label": "SMSIntegrationGenerator",
                  "config": {
                    "source_code": "async def generate_sms_integration(provider: str = 'kudocity', features: List[str] = None) -> str:\n    \"\"\"Generate SMS integration code for various providers\n    \n    Args:\n        provider: SMS provider (kudocity, twilio, aws_sns)\n        features: List of features to implement\n    \n    Returns:\n        str: SMS integration code template\n    \"\"\"\n    if features is None:\n        features = ['send_sms', 'receive_sms', 'delivery_status']\n    \n    if provider.lower() == 'kudocity':\n        template = '''import httpx\nfrom typing import Dict, Optional\nfrom pydantic import BaseModel\n\nclass KudocitySMSClient:\n    def __init__(self, api_key: str, base_url: str = \"https://api.kudocity.com/v1\"):\n        self.api_key = api_key\n        self.base_url = base_url\n        self.headers = {\n            \"Authorization\": f\"Bearer {api_key}\",\n            \"Content-Type\": \"application/json\"\n        }\n    \n    async def send_sms(self, to: str, message: str, from_number: Optional[str] = None) -> Dict:\n        \"\"\"Send SMS using Kudocity API\"\"\"\n        payload = {\n            \"to\": to,\n            \"message\": message\n        }\n        if from_number:\n            payload[\"from\"] = from_number\n        \n        async with httpx.AsyncClient() as client:\n            response = await client.post(\n                f\"{self.base_url}/sms/send\",\n                headers=self.headers,\n                json=payload\n            )\n            return response.json()\n    \n    async def get_delivery_status(self, message_id: str) -> Dict:\n        \"\"\"Get SMS delivery status\"\"\"\n        async with httpx.AsyncClient() as client:\n            response = await client.get(\n                f\"{self.base_url}/sms/status/{message_id}\",\n                headers=self.headers\n            )\n            return response.json()\n\n# FastAPI integration\nfrom fastapi import FastAPI, HTTPException\nfrom pydantic import BaseModel\n\nclass SMSRequest(BaseModel):\n    to: str\n    message: str\n    from_number: Optional[str] = None\n\napp = FastAPI()\nsms_client = KudocitySMSClient(api_key=\"your_api_key\")\n\<EMAIL>(\"/sms/send\")\nasync def send_sms_endpoint(request: SMSRequest):\n    try:\n        result = await sms_client.send_sms(\n            to=request.to,\n            message=request.message,\n            from_number=request.from_number\n        )\n        return result\n    except Exception as e:\n        raise HTTPException(status_code=500, detail=str(e))\n'''\n    else:\n        template = f\"# SMS integration template for {provider}\\n# Implementation needed for this provider\"\n    \n    return template",
                    "name": "generate_sms_integration",
                    "description": "Generate SMS integration code for various providers including Kudocity",
                    "global_imports": [
                      {
                        "module": "typing",
                        "imports": [
                          "List"
                        ]
                      }
                    ],
                    "has_cancellation_support": false
                  }
                }
              ]
            }
          },
          "model_context": {
            "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
            "component_type": "chat_completion_context",
            "version": 1,
            "component_version": 1,
            "description": "An unbounded chat completion context that keeps a view of all messages.",
            "label": "UnboundedChatCompletionContext",
            "config": {}
          },
          "description": "SMS Integration and External Services Specialist",
          "system_message": "You are an SMS Integration and External Services Specialist. Your expertise includes:\n1. SMS service integration (Kudocity, Twilio, AWS SNS, etc.)\n2. Webhook handling for incoming SMS and delivery notifications\n3. Message queuing and batch processing\n4. Rate limiting and compliance management\n5. Multi-channel communication (SMS, email, push notifications)\n6. External API integrations and service orchestration\n7. Authentication and security for third-party services\n8. Error handling and retry mechanisms\n9. Monitoring and logging for external service calls\n10. Cost optimization for communication services\nFocus on reliable, scalable integration patterns with proper error handling.",
          "model_client_stream": false,
          "reflect_on_tool_use": false,
          "tool_call_summary_format": "{result}",
          "metadata": {}
        }
      },
      {
        "provider": "autogen_agentchat.agents.AssistantAgent",
        "component_type": "agent",
        "version": 1,
        "component_version": 1,
        "description": "Supabase and database specialist",
        "label": "Supabase_Specialist",
        "config": {
          "name": "supabase_specialist",
          "model_client": {
            "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
            "component_type": "model",
            "version": 1,
            "component_version": 1,
            "description": "Chat completion client for OpenAI hosted models.",
            "label": "OpenAIChatCompletionClient",
            "config": {
              "temperature": 0.2,
              "model": "gpt-4o"
            }
          },
          "workbench": {
            "provider": "autogen_core.tools.StaticWorkbench",
            "component_type": "workbench",
            "version": 1,
            "component_version": 1,
            "description": "A workbench for Supabase and database tools",
            "label": "StaticWorkbench",
            "config": {
              "tools": [
                {
                  "provider": "autogen_core.tools.FunctionTool",
                  "component_type": "tool",
                  "version": 1,
                  "component_version": 1,
                  "description": "Generate Supabase database schema",
                  "label": "SupabaseSchemaGenerator",
                  "config": {
                    "source_code": "async def generate_supabase_schema(tables: List[Dict[str, Any]], enable_vector: bool = True) -> str:\n    \"\"\"Generate Supabase database schema with vector support\n    \n    Args:\n        tables: List of table definitions\n        enable_vector: Enable pgvector extension for vector operations\n    \n    Returns:\n        str: SQL schema for Supabase\n    \"\"\"\n    schema_sql = \"-- Supabase Database Schema\\n\\n\"\n    \n    if enable_vector:\n        schema_sql += \"-- Enable pgvector extension\\nCREATE EXTENSION IF NOT EXISTS vector;\\n\\n\"\n    \n    # Add RLS and security\n    schema_sql += \"-- Enable Row Level Security\\nALTER DATABASE postgres SET row_security = on;\\n\\n\"\n    \n    for table in tables:\n        table_name = table.get('name', 'unknown_table')\n        columns = table.get('columns', [])\n        \n        schema_sql += f\"-- Create {table_name} table\\n\"\n        schema_sql += f\"CREATE TABLE IF NOT EXISTS {table_name} (\\n\"\n        \n        # Add default columns\n        schema_sql += \"    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\\n\"\n        schema_sql += \"    created_at TIMESTAMPTZ DEFAULT NOW(),\\n\"\n        schema_sql += \"    updated_at TIMESTAMPTZ DEFAULT NOW(),\\n\"\n        \n        # Add custom columns\n        for col in columns:\n            col_name = col.get('name')\n            col_type = col.get('type', 'TEXT')\n            col_constraints = col.get('constraints', '')\n            schema_sql += f\"    {col_name} {col_type} {col_constraints},\\n\"\n        \n        # Add vector column if needed
        if enable_vector and table.get('has_vector', False):
            schema_sql += "    embedding vector(1536),\\n"
        
        schema_sql = schema_sql.rstrip(',\\n') + "\\n);\\n\\n"
        
        # Add indexes
        if 'indexes' in table:
            for index in table['indexes'
                    ]:
                schema_sql += f"CREATE INDEX IF NOT EXISTS idx_{table_name}_{index} ON {table_name}({index});\\n"
        
        # Add vector index if applicable
        if enable_vector and table.get('has_vector', False):
            schema_sql += f"CREATE INDEX IF NOT EXISTS idx_{table_name}_embedding ON {table_name} USING ivfflat (embedding vector_cosine_ops);\\n"
        
        # Add RLS policies
        schema_sql += f"ALTER TABLE {table_name} ENABLE ROW LEVEL SECURITY;\\n"
        schema_sql += f"CREATE POLICY \\"Enable read access for authenticated users\\" ON {table_name} FOR SELECT USING (auth.role() = 'authenticated');\\n\\n"
        
        # Add updated_at trigger
        schema_sql += f"""-- Create updated_at trigger for {table_name}
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$ language 'plpgsql';

CREATE TRIGGER update_{table_name
                    }_updated_at BEFORE UPDATE ON {table_name
                    } FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

"""
    
    return schema_sql","name": "generate_supabase_schema","description": "Generate Supabase database schema with vector support and RLS",
                    "global_imports": [
                      {
                        "module": "typing",
                        "imports": [
                          "List",
                          "Dict",
                          "Any"
                        ]
                      }
                    ],
                    "has_cancellation_support": false
                  }
                }
              ]
            }
          },
          "model_context": {
            "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
            "component_type": "chat_completion_context",
            "version": 1,
            "component_version": 1,
            "description": "An unbounded chat completion context that keeps a view of all messages.",
            "label": "UnboundedChatCompletionContext",
            "config": {}
          },
          "description": "Supabase and Database Specialist",
          "system_message": "You are a Supabase and Database Specialist. Your expertise includes:\\n1. Supabase setup, configuration, and optimization\\n2. PostgreSQL database design and performance tuning\\n3. pgvector extension for vector storage and similarity search\\n4. Row Level Security (RLS) and authentication policies\\n5. Real-time subscriptions and database triggers\\n6. Supabase Auth integration and user management\\n7. Edge Functions and serverless database operations\\n8. Database migrations and schema management\\n9. Backup, recovery, and monitoring strategies\\n10. Integration with Python applications using supabase-py\\nFocus on scalable, secure database architectures optimized for AI applications.",
          "model_client_stream": false,
          "reflect_on_tool_use": false,
          "tool_call_summary_format": "{result}",
          "metadata": {}
        }
      },
      {
        "provider": "autogen_agentchat.agents.AssistantAgent",
        "component_type": "agent",
        "version": 1,
        "component_version": 1,
        "description": "Quality assurance and testing specialist",
        "label": "QA_Tester",
        "config": {
          "name": "qa_tester",
          "model_client": {
            "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
            "component_type": "model",
            "version": 1,
            "component_version": 1,
            "description": "Chat completion client for OpenAI hosted models.",
            "label": "OpenAIChatCompletionClient",
            "config": {
              "temperature": 0.1,
              "model": "gpt-4o"
            }
          },
          "workbench": {
            "provider": "autogen_core.tools.StaticWorkbench",
            "component_type": "workbench",
            "version": 1,
            "component_version": 1,
            "description": "A workbench for testing and QA tools",
            "label": "StaticWorkbench",
            "config": {
              "tools": [
                {
                  "provider": "autogen_core.tools.FunctionTool",
                  "component_type": "tool",
                  "version": 1,
                  "component_version": 1,
                  "description": "Generate comprehensive test suite",
                  "label": "TestSuiteGenerator",
                  "config": {
                    "source_code": "async def generate_test_suite(components: List[str], test_types: List[str] = None) -> Dict[str, str]:\n    \"\"\"Generate comprehensive test suite for AI application components\n    \n    Args:\n        components: List of application components to test\n        test_types: Types of tests to generate (unit, integration, load, security)\n    \n    Returns:\n        Dict: Test files organized by component and test type\n    \"\"\"\n    if test_types is None:\n        test_types = ['unit', 'integration', 'load', 'security']\n    \n    test_files = {}\n    \n    for component in components:\n        component_tests = {}\n        \n        if 'unit' in test_types:\n            component_tests['unit'] = f'''import pytest\nimport asyncio\nfrom unittest.mock import Mock, AsyncMock, patch\nfrom fastapi.testclient import TestClient\n\nclass Test{component.title().replace('_', '')}Unit:\n    \"\"\"Unit tests for {component} component\"\"\"\n    \n    def setup_method(self):\n        \"\"\"Set up test fixtures\"\"\"\n        self.mock_data = {{}}\n    \n    def test_{component}_initialization(self):\n        \"\"\"Test {component} initialization\"\"\"\n        # Test implementation needed\n        assert True\n    \n    @pytest.mark.asyncio\n    async def test_{component}_async_operation(self):\n        \"\"\"Test async operations in {component}\"\"\"\n        # Test implementation needed\n        assert True\n    \n    def test_{component}_error_handling(self):\n        \"\"\"Test error handling in {component}\"\"\"\n        # Test implementation needed\n        assert True\n'''\n        \n        if 'integration' in test_types:\n            component_tests['integration'] = f'''import pytest\nimport httpx\nfrom fastapi.testclient import TestClient\nfrom sqlalchemy import create_engine\nfrom sqlalchemy.orm import sessionmaker\n\nclass Test{component.title().replace('_', '')}Integration:\n    \"\"\"Integration tests for {component} component\"\"\"\n    \n    @pytest.fixture(scope=\"class\")\n    def test_client(self):\n        \"\"\"Create test client for integration tests\"\"\"\n        from main import app\n        return TestClient(app)\n    \n    @pytest.fixture(scope=\"class\")\n    def test_database(self):\n        \"\"\"Set up test database\"\"\"\n        # Database setup for testing\n        pass\n    \n    def test_{component}_api_endpoints(self, test_client):\n        \"\"\"Test API endpoints for {component}\"\"\"\n        response = test_client.get(f\"/{component}\")\n        assert response.status_code in [200, 404]  # Adjust based on expected behavior\n    \n    def test_{component}_database_operations(self, test_database):\n        \"\"\"Test database operations for {component}\"\"\"\n        # Database operation tests\n        assert True\n'''\n        \n        if 'load' in test_types:\n            component_tests['load'] = f'''import asyncio\nimport aiohttp\nimport time\nfrom concurrent.futures import ThreadPoolExecutor\n\nclass Test{component.title().replace('_', '')}Load:\n    \"\"\"Load tests for {component} component\"\"\"\n    \n    async def test_{component}_concurrent_requests(self):\n        \"\"\"Test concurrent request handling\"\"\"\n        async def make_request():\n            async with aiohttp.ClientSession() as session:\n                async with session.get(f\"http://localhost:8000/{component}\") as response:\n                    return response.status\n        \n        # Run 100 concurrent requests\n        tasks = [make_request() for _ in range(100)]\n        start_time = time.time()\n        results = await asyncio.gather(*tasks, return_exceptions=True)\n        end_time = time.time()\n        \n        success_count = sum(1 for r in results if r == 200)\n        print(f\"Success rate: {{success_count}}/100\")\n        print(f\"Response time: {{end_time - start_time:.2f}}s\")\n        \n        assert success_count >= 95  # 95% success rate threshold\n'''\n        \n        if 'security' in test_types:\n            component_tests['security'] = f'''import pytest\nimport jwt\nfrom fastapi.testclient import TestClient\n\nclass Test{component.title().replace('_', '')}Security:\n    \"\"\"Security tests for {component} component\"\"\"\n    \n    def test_{component}_authentication_required(self, test_client):\n        \"\"\"Test that authentication is required\"\"\"\n        response = test_client.get(f\"/{component}\")\n        # Adjust based on your auth strategy\n        assert response.status_code in [401, 403] or \"auth\" in response.text.lower()\n    \n    def test_{component}_input_validation(self, test_client):\n        \"\"\"Test input validation and sanitization\"\"\"\n        malicious_inputs = [\n            \"<script>alert('xss')</script>\",\n            \"'; DROP TABLE users; --\",\n            \"{{__import__('os').system('rm -rf /')}}\"\n        ]\n        \n        for malicious_input in malicious_inputs:\n            response = test_client.post(f\"/{component}\", json={{\"data\": malicious_input}})\n            # Should not execute malicious code\n            assert response.status_code in [400, 422, 200]\n    \n    def test_{component}_rate_limiting(self, test_client):\n        \"\"\"Test rate limiting protection\"\"\"\n        # Make rapid requests to test rate limiting\n        responses = []\n        for _ in range(50):\n            response = test_client.get(f\"/{component}\")\n            responses.append(response.status_code)\n        \n        # Should have some rate limited responses\n        rate_limited = sum(1 for r in responses if r == 429)\n        assert rate_limited > 0 or len(set(responses)) == 1  # Either rate limited or all same response\n'''\n        \n        test_files[f\"test_{component}\"] = component_tests\n    \n    return test_files",
                    "name": "generate_test_suite",
                    "description": "Generate comprehensive test suites for AI application components",
                    "global_imports": [
                      {
                        "module": "typing",
                        "imports": [
                          "List",
                          "Dict"
                        ]
                      }
                    ],
                    "has_cancellation_support": false
                  }
                }
              ]
            }
          },
          "model_context": {
            "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
            "component_type": "chat_completion_context",
            "version": 1,
            "component_version": 1,
            "description": "An unbounded chat completion context that keeps a view of all messages.",
            "label": "UnboundedChatCompletionContext",
            "config": {}
          },
          "description": "Quality Assurance and Testing Specialist",
          "system_message": "You are a Quality Assurance and Testing Specialist for AI applications. Your expertise includes:\\n1. Comprehensive test strategy design and implementation\\n2. Unit testing with pytest, mocking, and fixtures\\n3. Integration testing for APIs, databases, and external services\\n4. Load testing and performance validation\\n5. Security testing and vulnerability assessment\\n6. AI/ML model testing and validation strategies\\n7. Test automation and CI/CD pipeline integration\\n8. API testing with FastAPI TestClient and httpx\\n9. Database testing and data validation\\n10. Error handling and edge case testing\\n11. RAG system testing for accuracy and relevance\\n12. Vector database performance testing\\nFocus on building robust, maintainable test suites that ensure application reliability and performance.",
          "model_client_stream": false,
          "reflect_on_tool_use": false,
          "tool_call_summary_format": "{result}",
          "metadata": {}
        }
      },
      {
        "provider": "autogen_agentchat.agents.AssistantAgent",
        "component_type": "agent",
        "version": 1,
        "component_version": 1,
        "description": "DevOps and deployment specialist",
        "label": "DevOps_Specialist",
        "config": {
          "name": "devops_specialist",
          "model_client": {
            "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
            "component_type": "model",
            "version": 1,
            "component_version": 1,
            "description": "Chat completion client for OpenAI hosted models.",
            "label": "OpenAIChatCompletionClient",
            "config": {
              "temperature": 0.2,
              "model": "gpt-4o"
            }
          },
          "workbench": {
            "provider": "autogen_core.tools.StaticWorkbench",
            "component_type": "workbench",
            "version": 1,
            "component_version": 1,
            "description": "A workbench for DevOps and deployment tools",
            "label": "StaticWorkbench",
            "config": {
              "tools": [
                {
                  "provider": "autogen_core.tools.FunctionTool",
                  "component_type": "tool",
                  "version": 1,
                  "component_version": 1,
                  "description": "Generate deployment configurations",
                  "label": "DeploymentConfigGenerator",
                  "config": {
                    "source_code": "async def generate_deployment_config(platform: str = 'docker', services: List[str] = None) -> Dict[str, str]:\n    \"\"\"Generate deployment configurations for various platforms\n    \n    Args:\n        platform: Deployment platform (docker, kubernetes, vercel, railway)\n        services: List of services to deploy\n    \n    Returns:\n        Dict: Deployment configuration files\n    \"\"\"\n    if services is None:\n        services = ['api', 'database', 'vector_db', 'worker']\n    \n    configs = {}\n    \n    if platform.lower() == 'docker':\n        # Dockerfile for FastAPI app\n        configs['Dockerfile'] = '''FROM python:3.11-slim\n\nWORKDIR /app\n\n# Install system dependencies\nRUN apt-get update && apt-get install -y \\\\\n    gcc \\\\\n    && rm -rf /var/lib/apt/lists/*\n\n# Copy requirements first for better caching\nCOPY requirements.txt .\nRUN pip install --no-cache-dir -r requirements.txt\n\n# Copy application code\nCOPY . .\n\n# Create non-root user\nRUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app\nUSER appuser\n\n# Expose port\nEXPOSE 8000\n\n# Run application\nCMD [\"uvicorn\", \"main:app\", \"--host\", \"0.0.0.0\", \"--port\", \"8000\"]\n'''\n        \n        # Docker Compose\n        configs['docker-compose.yml'] = '''version: '3.8'\n\nservices:\n  api:\n    build: .\n    ports:\n      - \"8000:8000\"\n    environment:\n      - DATABASE_URL=**************************************/aiapp\n      - SUPABASE_URL=https://supabase.com/dashboard/project/sggfodllvfjuqammntnj\n      - SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNnZ2ZvZGxsdmZqdXFhbW1udG5qIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5NjAyOTYsImV4cCI6MjA2NTUzNjI5Nn0.zNIeImlYgY0gP77ZvC5umpVd5XrKQf6Wc4Nrrq5AkjA\n      - OPENAI_API_KEY=********************************************************************************************************************************************************************\n    depends_on:\n      - db\n      - redis\n    restart: unless-stopped\n  \n  db:\n    image: pgvector/pgvector:pg15\n    environment:\n      - POSTGRES_DB=aiapp\n      - POSTGRES_USER=postgres\n      - POSTGRES_PASSWORD=password\n    ports:\n      - \"5432:5432\"\n    volumes:\n      - postgres_data:/var/lib/postgresql/data\n    restart: unless-stopped\n  \n  redis:\n    image: redis:7-alpine\n    ports:\n      - \"6379:6379\"\n    restart: unless-stopped\n  \n  worker:\n    build: .\n    command: celery -A app.worker worker --loglevel=info\n    environment:\n      - DATABASE_URL=**************************************/aiapp\n      - REDIS_URL=redis://redis:6379/0\n    depends_on:\n      - db\n      - redis\n    restart: unless-stopped\n\nvolumes:\n  postgres_data:\n'''\n    \n    elif platform.lower() == 'kubernetes':\n        configs['deployment.yaml'] = '''apiVersion: apps/v1\nkind: Deployment\nmetadata:\n  name: ai-app-deployment\n  labels:\n    app: ai-app\nspec:\n  replicas: 3\n  selector:\n    matchLabels:\n      app: ai-app\n  template:\n    metadata:\n      labels:\n        app: ai-app\n    spec:\n      containers:\n      - name: ai-app\n        image: your-registry/ai-app:latest\n        ports:\n        - containerPort: 8000\n        env:\n        - name: DATABASE_URL\n          valueFrom:\n            secretKeyRef:\n              name: app-secrets\n              key: database-url\n        - name: OPENAI_API_KEY\n          valueFrom:\n            secretKeyRef:\n              name: app-secrets\n              key: openai-api-key\n        resources:\n          requests:\n            memory: \"512Mi\"\n            cpu: \"250m\"\n          limits:\n            memory: \"1Gi\"\n            cpu: \"500m\"\n        livenessProbe:\n          httpGet:\n            path: /health\n            port: 8000\n          initialDelaySeconds: 30\n          periodSeconds: 10\n        readinessProbe:\n          httpGet:\n            path: /health\n            port: 8000\n          initialDelaySeconds: 5\n          periodSeconds: 5\n---\napiVersion: v1\nkind: Service\nmetadata:\n  name: ai-app-service\nspec:\n  selector:\n    app: ai-app\n  ports:\n    - protocol: TCP\n      port: 80\n      targetPort: 8000\n  type: LoadBalancer\n'''\n    \n    # GitHub Actions CI/CD\n    configs['.github/workflows/deploy.yml'] = '''name: Deploy AI Application\n\non:\n  push:\n    branches: [ main ]\n  pull_request:\n    branches: [ main ]\n\njobs:\n  test:\n    runs-on: ubuntu-latest\n    \n    services:\n      postgres:\n        image: pgvector/pgvector:pg15\n        env:\n          POSTGRES_PASSWORD: postgres\n          POSTGRES_DB: test_db\n        options: >-\n          --health-cmd pg_isready\n          --health-interval 10s\n          --health-timeout 5s\n          --health-retries 5\n        ports:\n          - 5432:5432\n    \n    steps:\n    - uses: actions/checkout@v3\n    \n    - name: Set up Python\n      uses: actions/setup-python@v4\n      with:\n        python-version: '3.11'\n    \n    - name: Install dependencies\n      run: |\n        python -m pip install --upgrade pip\n        pip install -r requirements.txt\n        pip install pytest pytest-asyncio\n    \n    - name: Run tests\n      env:\n        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db\n      run: |\n        pytest\n    \n    - name: Run security scan\n      run: |\n        pip install bandit safety\n        bandit -r . -f json\n        safety check\n  \n  deploy:\n    needs: test\n    runs-on: ubuntu-latest\n    if: github.ref == 'refs/heads/main'\n    \n    steps:\n    - uses: actions/checkout@v3\n    \n    - name: Deploy to production\n      run: |\n        echo \"Deploy to your chosen platform\"\n        # Add your deployment commands here\n'''\n    \n    return configs",
                    "name": "generate_deployment_config",
                    "description": "Generate deployment configurations for various platforms",
                    "global_imports": [
                      {
                        "module": "typing",
                        "imports": [
                          "List",
                          "Dict"
                        ]
                      }
                    ],
                    "has_cancellation_support": false
                  }
                }
              ]
            }
          },
          "model_context": {
            "provider": "autogen_core.model_context.UnboundedChatCompletionContext",
            "component_type": "chat_completion_context",
            "version": 1,
            "component_version": 1,
            "description": "An unbounded chat completion context that keeps a view of all messages.",
            "label": "UnboundedChatCompletionContext",
            "config": {}
          },
          "description": "DevOps and Deployment Specialist",
          "system_message": "You are a DevOps and Deployment Specialist for AI applications. Your expertise includes:\\n1. Containerization with Docker and Docker Compose\\n2. Kubernetes orchestration and scaling strategies\\n3. CI/CD pipeline design and implementation\\n4. Cloud deployment (AWS, GCP, Azure, Railway, Vercel)\\n5. Infrastructure as Code (Terraform, CloudFormation)\\n6. Monitoring, logging, and observability setup\\n7. Security hardening and best practices\\n8. Performance optimization and auto-scaling\\n9. Database migration and backup strategies\\n10. Load balancing and high availability setup\\n11. Environment management and configuration\\n12. Cost optimization and resource management\\nFocus on building robust, scalable, and secure deployment pipelines for production AI applications.",
          "model_client_stream": false,
          "reflect_on_tool_use": false,
          "tool_call_summary_format": "{result}",
          "metadata": {}
        }
      }
    ],
    "model_client": {
      "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
      "component_type": "model",
      "version": 1,
      "component_version": 1,
      "description": "Chat completion client for OpenAI hosted models.",
      "label": "OpenAIChatCompletionClient",
      "config": {
        "temperature": 0.3,
        "model": "gpt-4o"
      }
    },
    "termination_condition": {
      "provider": "autogen_agentchat.base.OrTerminationCondition",
      "component_type": "termination",
      "version": 1,
      "component_version": 1,
      "label": "OrTerminationCondition",
      "config": {
        "conditions": [
          {
            "provider": "autogen_agentchat.conditions.TextMentionTermination",
            "component_type": "termination",
            "version": 1,
            "component_version": 1,
            "description": "Terminate the conversation if a specific text is mentioned.",
            "label": "TextMentionTermination",
            "config": {
              "text": "TERMINATE"
            }
          },
          {
            "provider": "autogen_agentchat.conditions.MaxMessageTermination",
            "component_type": "termination",
            "version": 1,
            "component_version": 1,
            "description": "Terminate the conversation after a maximum number of messages have been exchanged.",
            "label": "MaxMessageTermination",
            "config": {
              "max_messages": 50,
              "include_agent_event": false
            }
          }
        ]
      }
    },
    "selector_prompt": "You are coordinating an AI application development team by selecting the team member to speak/act next. The following specialized roles are available:\\n    {roles}\\n\\n    **Role Descriptions:**\\n    - ai_architect: Designs system architecture, plans integrations, creates technical specifications\\n    - fastapi_developer: Builds FastAPI backend, implements APIs, handles authentication and middleware\\n    - rag_ai_specialist: Implements RAG pipelines, LLM integration, embedding strategies\\n    - vector_expert: Manages vector databases, document analysis, similarity search optimization\\n    - sms_integration_specialist: Handles SMS services (Kudocity), webhooks, external API integrations\\n    - supabase_specialist: Manages Supabase setup, PostgreSQL optimization, pgvector, RLS policies\\n    - qa_tester: Creates comprehensive test suites, handles quality assurance, performance testing\\n    - devops_specialist: Manages deployment, CI/CD, containerization, infrastructure\\n\\n    **Selection Strategy:**\\n    1. **Planning Phase**: Start with ai_architect for system design\\n    2. **Development Phase**: Rotate between specialists based on current task\\n    3. **Integration Phase**: Coordinate between related specialists\\n    4. **Testing Phase**: Engage qa_tester for validation\\n    5. **Deployment Phase**: Engage devops_specialist for production readiness\\n\\n    **Guidelines:**\\n    - Select ai_architect for high-level design decisions and architecture planning\\n    - Choose technical specialists when working on their domain-specific components\\n    - Engage qa_tester when code/features need testing or validation\\n    - Select devops_specialist for deployment, CI/CD, and infrastructure concerns\\n    - Consider dependencies between components when selecting specialists\\n    - Ensure proper handoffs between related specialists (e.g., rag_ai_specialist → vector_expert)\\n\\n    Based on the conversation context and current development needs, select the most appropriate specialist.\\n\\n    {history}\\n\\n    Read the above conversation. Then select the next role from {participants} to play. ONLY RETURN THE ROLE.",
    "allow_repeated_speaker": true,
    "max_selector_attempts": 3,
    "emit_team_events": false,
    "model_client_streaming": false
  }
}