from fastapi import APIRouter, Depends, Query, status
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database_management import get_db
from app.core.models.pydantic_models.auth_models import (
    UserRegistrationRequest,
    UserLoginRequest,
    ForgotPasswordRequest,
    ResetPasswordRequest,
    StandardResponse,
    LoginResponse,
    UserResponse
)
from app.core.models.pydantic_models.response_model import ResponseModel
from app.core.models.API_response_models import (
    user_registration_response_model,
    user_login_response_model,
    user_logout_response_model,
    forgot_password_response_model,
    reset_password_response_model,
    validate_token_response_model
)
from app.core.models.sql_alchemy_models.user_models import User
from app.core.operations.auth.auth_operations import AuthOperations
from app.core.utils.authentication_manager.authentication import get_current_user
from app.core.utils.system_constants.system_messages import client_messages

# Create router for authentication endpoints
router = APIRouter(prefix="/auth", tags=["Authentication"])

# Initialize auth operations
auth_operations = AuthOperations()


@router.post(
    "/register",
    response_model=ResponseModel,
    responses=user_registration_response_model,
    status_code=status.HTTP_201_CREATED,
    summary="Register a new user",
    description="Create a new user account with email, mobile number, and password. Email and mobile number must be unique."
)
async def register_user(
    registration_data: UserRegistrationRequest,
    db: AsyncSession = Depends(get_db),
    language: str = Query(default="en", description="Response language (en, es, fr, etc.)")
):
    """
    Register a new user with the following requirements:
    - Unique email address
    - Unique mobile number
    - Strong password (min 8 chars, uppercase, lowercase, digit, special char)
    - Password confirmation must match
    """
    return await auth_operations.register_user(registration_data, db, language)


@router.post(
    "/login",
    response_model=ResponseModel,
    responses=user_login_response_model,
    status_code=status.HTTP_200_OK,
    summary="User login",
    description="Authenticate user with email/mobile and password. Returns JWT token for API access."
)
async def login_user(
    login_data: UserLoginRequest,
    db: AsyncSession = Depends(get_db),
    language: str = Query(default="en", description="Response language (en, es, fr, etc.)")
):
    """
    Authenticate user login:
    - Can use email or mobile number as identifier
    - Returns JWT access token on successful authentication
    - Remember me checkbox controls token expiry (30 minutes vs 30 days)
    """
    return await auth_operations.login_user(login_data, db, language)


@router.post(
    "/logout",
    response_model=ResponseModel,
    responses=user_logout_response_model,
    status_code=status.HTTP_200_OK,
    summary="User logout",
    description="Logout current user and invalidate session"
)
async def logout_user(
    current_user: User = Depends(get_current_user),
    language: str = Query(default="en", description="Response language (en, es, fr, etc.)")
):
    """
    Logout current authenticated user:
    - Requires valid JWT token
    - Simulates token invalidation (in production, would blacklist token)
    """
    return await auth_operations.logout_user(str(current_user['user_id']), language)


@router.post(
    "/forgot-password",
    response_model=ResponseModel,
    responses=forgot_password_response_model,
    status_code=status.HTTP_200_OK,
    summary="Initiate password reset",
    description="Request password reset token for user account"
)
async def forgot_password(
    forgot_data: ForgotPasswordRequest,
    db: AsyncSession = Depends(get_db),
    language: str = Query(default="en", description="Response language (en, es, fr, etc.)")
):
    """
    Initiate password reset process:
    - Can use email or mobile number as identifier
    - Generates secure reset token (normally sent via email)
    - Token expires after 1 hour
    """
    return await auth_operations.forgot_password(forgot_data, db, language)


@router.post(
    "/reset-password",
    response_model=ResponseModel,
    responses=reset_password_response_model,
    status_code=status.HTTP_200_OK,
    summary="Reset password",
    description="Reset user password using reset token"
)
async def reset_password(
    reset_data: ResetPasswordRequest,
    db: AsyncSession = Depends(get_db),
    language: str = Query(default="en", description="Response language (en, es, fr, etc.)")
):
    """
    Reset user password:
    - Requires valid reset token from forgot-password endpoint
    - New password must meet strength requirements
    - Password confirmation must match
    - Token is consumed after successful reset
    """
    return await auth_operations.reset_password(reset_data, db, language)


@router.get(
    "/validate-token",
    response_model=ResponseModel,
    responses=validate_token_response_model,
    status_code=status.HTTP_200_OK,
    summary="Validate JWT token",
    description="Validate current JWT token and return user info"
)
async def validate_token(
    current_user: User = Depends(get_current_user),
    language: str = Query(default="en", description="Response language (en, es, fr, etc.)")
):
    """
    Validate current JWT token:
    - Checks if token is valid and not expired
    - Returns user information if token is valid
    - Useful for frontend token validation
    """
    return await auth_operations.validate_token(current_user, language) 