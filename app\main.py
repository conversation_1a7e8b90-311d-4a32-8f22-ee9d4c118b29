from fastapi import <PERSON><PERSON><PERSON>, HTT<PERSON><PERSON>xception, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
from app.core.middleware.logging_middleware import LoggingMiddleware
from app.core.utils.logger.logger import logger

from app.core.config import settings
from app.api.v1 import auth
from app.core.models.pydantic_models.response_model import ResponseModel, MessageModel, DataModel
from app.core.utils.exception_manager.exception_handler import exception_handler_obj
from app.core.utils.exception_manager.custom_exceptions import GrowthHiveException

# Create FastAPI application with custom configuration
app = FastAPI(
    title="Growth Hive Auth API",
    description="""
    **Growth Hive Authentication API** - A production-ready FastAPI authentication system
    
    ## Features
    
    * **User Registration** - Create new user accounts with email and mobile verification
    * **JWT Authentication** - Secure token-based authentication with configurable expiry
    * **Password Management** - Forgot/reset password functionality with secure tokens
    * **Profile Management** - User profile retrieval and management
    * **Token Validation** - Endpoint for validating JWT tokens
    
    ## Authentication
    
    This API uses JWT (JSON Web Tokens) for authentication. After successful login, you'll receive an access token that should be included in the `Authorization` header as `Bearer <token>` for all protected endpoints.
    
    ## Password Requirements
    
    Passwords must meet the following criteria:
    - Minimum 8 characters
    - At least one uppercase letter
    - At least one lowercase letter  
    - At least one digit
    - At least one special character
    
    ## Remember Me
    
    The login endpoint includes a "remember_me" option:
    - `true` (default): Token expires in 30 days
    - `false`: Token expires in 30 minutes
    """,
    version="1.0.0",
    contact={
        "name": "Growth Hive API Support",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
    docs_url="/docs",  # Swagger UI URL
    redoc_url="/redoc",  # ReDoc URL
    openapi_url="/openapi.json"
)

# Global exception handler
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Global HTTP exception handler"""
    try:
        # Convert HTTPException to GrowthHiveException format
        response = ResponseModel(
            success=False,
            message=MessageModel(
                title="Error",
                description=str(exc.detail)
            ),
            data=DataModel(details=None),
            error_code=exc.status_code
        )
        return JSONResponse(
            status_code=exc.status_code,
            content=response.model_dump()
        )
    except Exception as e:
        # Fallback for any errors in HTTP exception handling
        return await exception_handler_obj.manage_exception(
            "unknown_error",
            message=f"Error handling HTTP exception: {str(e)}",
            language="en"
        )

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler for unhandled exceptions"""
    try:
        return await exception_handler_obj.manage_exception(
            "unknown_error",
            message=f"An unexpected error occurred: {str(exc)}",
            language="en"
        )
    except Exception as e:
        # Fallback for any errors in global exception handling
        fallback_response = ResponseModel(
            success=False,
            message=MessageModel(
                title="Internal Server Error",
                description="An error occurred while handling the exception. Please try again or contact support if the issue persists."
            ),
            data=DataModel(details=None),
            error_code=5000
        )
        return JSONResponse(
            content=fallback_response.model_dump(),
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@app.exception_handler(GrowthHiveException)
async def growth_hive_exception_handler(request: Request, exc: GrowthHiveException):
    """Handle GrowthHiveException globally"""
    try:
        return JSONResponse(
            status_code=exc.status_code,
            content=exc.detail
        )
    except Exception as e:
        # Fallback for any errors in GrowthHiveException handling
        return await exception_handler_obj.manage_exception(
            "unknown_error",
            message=f"Error handling GrowthHiveException: {str(e)}",
            language="en"
        )

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Add logging middleware
app.add_middleware(LoggingMiddleware)

# Include routers
app.include_router(auth.router, prefix="/api/v1")

# Root endpoint
@app.get(
    "/",
    response_model=ResponseModel,
    status_code=status.HTTP_200_OK,
    tags=["Root"],
    summary="API Root",
    description="Welcome endpoint with API information"
)
async def root():
    """Welcome endpoint that provides basic API information and health status."""
    try:
        return ResponseModel(
            success=True,
            message=MessageModel(
                title="Welcome to Growth Hive API",
                description="API is running successfully"
            ),
            data=DataModel(
                details={
                    "service": "Growth Hive Auth API",
                    "version": "1.0.0",
                    "status": "healthy",
                    "docs_url": "/docs",
                    "redoc_url": "/redoc",
                    "api_prefix": "/api/v1"
                }
            ),
            error_code=0
        )
    except Exception as e:
        return await exception_handler_obj.manage_exception(
            "unknown_error",
            message=f"Error in root endpoint: {str(e)}",
            language="en"
        )

# Health check endpoint
@app.get(
    "/health",
    response_model=ResponseModel,
    status_code=status.HTTP_200_OK,
    tags=["Health"],
    summary="Health Check",
    description="Check API health status"
)
async def health_check():
    """Health check endpoint for monitoring and load balancers."""
    try:
        return ResponseModel(
            success=True,
            message=MessageModel(
                title="Health Check",
                description="Service is healthy and running"
            ),
            data=DataModel(
                details={
                    "status": "healthy",
                    "service": "Growth Hive Auth API",
                    "version": "1.0.0"
                }
            ),
            error_code=0
        )
    except Exception as e:
        return await exception_handler_obj.manage_exception(
            "unknown_error",
            message=f"Error in health check endpoint: {str(e)}",
            language="en"
        )

@app.on_event("startup")
async def startup_event():
    """Log application startup"""
    logger.info("Application startup")

@app.on_event("shutdown")
async def shutdown_event():
    """Log application shutdown"""
    logger.info("Application shutdown")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"
    ) 