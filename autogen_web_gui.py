#!/usr/bin/env python3
"""
AutoGen Web GUI - Remote execution with real-time progress and communication updates
Similar to AutoGen Studio but custom for GrowthHive project
"""

import asyncio
import json
import os
import threading
import time
from datetime import datetime
from typing import Dict, List, Any
from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
import uvicorn

# AutoGen imports
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import Selector<PERSON><PERSON>Chat
from autogen_agentchat.conditions import MaxMessageTermination
from autogen_ext.models.openai import OpenAIChatCompletionClient

app = FastAPI(title="AutoGen Web GUI", description="Remote AutoGen execution with real-time updates")

# Create templates and static directories
os.makedirs("templates", exist_ok=True)
os.makedirs("static", exist_ok=True)

templates = Jinja2Templates(directory="templates")

class AutoGenWebManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.development_active = False
        self.current_phase = "Idle"
        self.progress = 0
        self.messages = []
        self.agents_status = {}
        self.task_counter = 0
        
    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        
        # Send current status to new connection
        await self.send_status_update()
        
    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
    
    async def broadcast_message(self, message: Dict[str, Any]):
        """Broadcast message to all connected clients"""
        if self.active_connections:
            for connection in self.active_connections[:]:  # Copy list to avoid modification during iteration
                try:
                    await connection.send_text(json.dumps(message))
                except:
                    # Remove disconnected clients
                    self.active_connections.remove(connection)
    
    async def send_status_update(self):
        """Send current status to all clients"""
        status = {
            "type": "status_update",
            "data": {
                "development_active": self.development_active,
                "current_phase": self.current_phase,
                "progress": self.progress,
                "agents_status": self.agents_status,
                "task_counter": self.task_counter,
                "timestamp": datetime.now().isoformat()
            }
        }
        await self.broadcast_message(status)
    
    async def send_agent_message(self, agent_name: str, message: str, message_type: str = "info"):
        """Send agent message to all clients"""
        self.task_counter += 1
        
        message_data = {
            "type": "agent_message",
            "data": {
                "agent_name": agent_name,
                "message": message,
                "message_type": message_type,
                "task_id": self.task_counter,
                "timestamp": datetime.now().isoformat()
            }
        }
        
        self.messages.append(message_data["data"])
        await self.broadcast_message(message_data)
    
    async def update_progress(self, phase: str, progress: int):
        """Update development progress"""
        self.current_phase = phase
        self.progress = progress
        await self.send_status_update()
    
    async def start_development(self, project_requirements: str):
        """Start the AutoGen development process"""
        self.development_active = True
        self.current_phase = "Initializing"
        self.progress = 0
        self.messages = []
        self.task_counter = 0
        
        await self.send_status_update()
        await self.send_agent_message("System", "Starting AutoGen development team...", "system")
        
        # Run development in background
        asyncio.create_task(self.run_autogen_team(project_requirements))
    
    async def run_autogen_team(self, project_requirements: str):
        """Run the AutoGen team development process"""
        try:
            # Set up environment
            os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"
            
            await self.update_progress("Creating AI Team", 10)
            
            # Create model client
            model_client = OpenAIChatCompletionClient(
                model="gpt-4o",
                temperature=0.3
            )
            
            # Create agents
            await self.send_agent_message("System", "Creating AI Architect...", "system")
            ai_architect = AssistantAgent(
                name="ai_architect",
                model_client=model_client,
                system_message="""You are an AI Architecture Specialist with expertise in designing scalable AI applications. Your responsibilities include:
1. Design overall system architecture for AI applications
2. Plan integration patterns for FastAPI, RAG, vector databases, and external services
3. Define data flow and component interactions
4. Ensure scalability, security, and performance considerations
5. Create technical specifications and architecture diagrams
6. Guide technology stack decisions
7. Plan deployment and infrastructure requirements
Focus on microservices architecture, cloud-native solutions, and best practices for AI/ML applications."""
            )
            
            await self.send_agent_message("System", "Creating FastAPI Developer...", "system")
            fastapi_developer = AssistantAgent(
                name="fastapi_developer",
                model_client=OpenAIChatCompletionClient(model="gpt-4o", temperature=0.2),
                system_message="""You are a FastAPI Backend Development Specialist. Your expertise includes:
1. Building high-performance FastAPI applications
2. Implementing RESTful APIs with proper documentation
3. Database integration with Supabase and PostgreSQL
4. Authentication and authorization (JWT, OAuth2)
5. Middleware implementation and request/response handling
6. API versioning and rate limiting
7. WebSocket implementation for real-time features
8. Integration with external services and APIs
9. Performance optimization and caching strategies
Focus on clean, maintainable code following FastAPI best practices."""
            )
            
            await self.send_agent_message("System", "Creating Supabase Specialist...", "system")
            supabase_specialist = AssistantAgent(
                name="supabase_specialist",
                model_client=OpenAIChatCompletionClient(model="gpt-4o", temperature=0.2),
                system_message="""You are a Supabase and Database Specialist. Your expertise includes:
1. Supabase setup, configuration, and optimization
2. PostgreSQL database design and performance tuning
3. pgvector extension for vector storage and similarity search
4. Row Level Security (RLS) and authentication policies
5. Real-time subscriptions and database triggers
6. Supabase Auth integration and user management
7. Edge Functions and serverless database operations
8. Database migrations and schema management
9. Backup, recovery, and monitoring strategies
10. Integration with Python applications using supabase-py
Focus on scalable, secure database architectures optimized for AI applications."""
            )
            
            await self.send_agent_message("System", "Creating RAG AI Specialist...", "system")
            rag_ai_specialist = AssistantAgent(
                name="rag_ai_specialist",
                model_client=OpenAIChatCompletionClient(model="gpt-4o", temperature=0.2),
                system_message="""You are a RAG (Retrieval-Augmented Generation) and AI Integration Specialist. Your expertise includes:
1. Designing and implementing RAG pipelines for intelligent document retrieval
2. LLM integration with OpenAI, Anthropic, and other AI models
3. Embedding strategies and semantic search optimization
4. Context management and prompt engineering
5. AI model fine-tuning and optimization
6. Knowledge base construction and maintenance
7. Intelligent FAQ systems and chatbot development
8. AI-powered content generation and summarization
9. Multi-modal AI integration (text, image, audio)
10. AI model evaluation and performance monitoring
Focus on creating intelligent, context-aware AI systems that provide accurate and relevant responses."""
            )
            
            await self.update_progress("Team Created", 20)
            
            # Create termination condition
            termination = MaxMessageTermination(max_messages=50)
            
            # Create team
            team = SelectorGroupChat(
                participants=[ai_architect, fastapi_developer, supabase_specialist, rag_ai_specialist],
                model_client=model_client,
                termination_condition=termination
            )
            
            await self.update_progress("Starting Development", 30)
            await self.send_agent_message("System", "AutoGen team created successfully! Starting development...", "success")
            
            # Run the team with custom message handler
            stream = team.run_stream(task=project_requirements)
            
            await self.update_progress("Development In Progress", 40)
            
            # Process messages from the stream
            async for message in stream:
                await self.process_autogen_message(message)
            
            await self.update_progress("Development Complete", 100)
            await self.send_agent_message("System", "Development completed successfully!", "success")
            
        except Exception as e:
            await self.send_agent_message("System", f"Error during development: {str(e)}", "error")
        finally:
            self.development_active = False
            await self.send_status_update()
    
    async def process_autogen_message(self, message):
        """Process messages from AutoGen stream"""
        try:
            if hasattr(message, 'source') and hasattr(message, 'content'):
                agent_name = message.source
                content = message.content
                
                # Determine message type based on content
                if "```" in content:
                    message_type = "code"
                elif any(keyword in content.lower() for keyword in ['error', 'failed', 'issue']):
                    message_type = "error"
                elif any(keyword in content.lower() for keyword in ['complete', 'finished', 'done']):
                    message_type = "success"
                elif any(keyword in content.lower() for keyword in ['design', 'plan', 'architecture']):
                    message_type = "design"
                else:
                    message_type = "info"
                
                await self.send_agent_message(agent_name, content, message_type)
                
                # Update agent status
                self.agents_status[agent_name] = {
                    "status": "active",
                    "last_message": content[:100] + "..." if len(content) > 100 else content,
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            await self.send_agent_message("System", f"Error processing message: {str(e)}", "error")

# Global manager instance
autogen_manager = AutoGenWebManager()

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await autogen_manager.connect(websocket)
    try:
        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()
            message = json.loads(data)
            
            if message.get("type") == "start_development":
                requirements = message.get("requirements", "")
                await autogen_manager.start_development(requirements)
            elif message.get("type") == "stop_development":
                autogen_manager.development_active = False
                await autogen_manager.send_status_update()
                
    except WebSocketDisconnect:
        autogen_manager.disconnect(websocket)

@app.get("/", response_class=HTMLResponse)
async def get_dashboard(request: Request):
    return templates.TemplateResponse("dashboard.html", {"request": request})

if __name__ == "__main__":
    # Create the HTML template
    html_template = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoGen Web GUI - GrowthHive Development</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .dashboard { display: grid; grid-template-columns: 1fr 2fr; gap: 20px; height: calc(100vh - 120px); }
        .control-panel { background: white; border-radius: 10px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .chat-area { background: white; border-radius: 10px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); display: flex; flex-direction: column; }
        .progress-bar { width: 100%; height: 20px; background: #e0e0e0; border-radius: 10px; margin: 10px 0; overflow: hidden; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #4CAF50, #45a049); transition: width 0.3s ease; }
        .status-card { background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .messages-container { flex: 1; overflow-y: auto; border: 1px solid #ddd; border-radius: 5px; padding: 10px; margin: 10px 0; max-height: 500px; }
        .message { margin: 10px 0; padding: 10px; border-radius: 8px; }
        .message.system { background: #e3f2fd; border-left: 4px solid #2196f3; }
        .message.info { background: #f3e5f5; border-left: 4px solid #9c27b0; }
        .message.success { background: #e8f5e8; border-left: 4px solid #4caf50; }
        .message.error { background: #ffebee; border-left: 4px solid #f44336; }
        .message.code { background: #f5f5f5; border-left: 4px solid #ff9800; font-family: monospace; }
        .agent-name { font-weight: bold; color: #333; margin-bottom: 5px; }
        .timestamp { font-size: 0.8em; color: #666; }
        .btn { background: #007bff; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer; font-size: 16px; }
        .btn:hover { background: #0056b3; }
        .btn:disabled { background: #ccc; cursor: not-allowed; }
        textarea { width: 100%; height: 150px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; resize: vertical; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 AutoGen Web GUI</h1>
        <p>Remote AutoGen Execution for GrowthHive AI Prospect Outreach System</p>
    </div>
    
    <div class="container">
        <div class="dashboard">
            <div class="control-panel">
                <h2>🎛️ Control Panel</h2>
                
                <div class="status-card">
                    <h3>📊 Development Status</h3>
                    <p><strong>Phase:</strong> <span id="current-phase">Idle</span></p>
                    <p><strong>Progress:</strong> <span id="progress-text">0%</span></p>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-bar" style="width: 0%"></div>
                    </div>
                    <p><strong>Active:</strong> <span id="development-status">No</span></p>
                </div>
                
                <div class="status-card">
                    <h3>🤖 Agent Status</h3>
                    <div id="agents-status">
                        <p>No agents active</p>
                    </div>
                </div>
                
                <div class="status-card">
                    <h3>🚀 Start Development</h3>
                    <textarea id="requirements" placeholder="Enter project requirements here...">Build an AI Enabled Prospect Outreach System (Phase 1) with the following requirements:

**PROJECT OVERVIEW:**
- Purpose: AI-enabled prospect outreach system to identify lead intent and provide prequalified leads
- Communication: SMS-based communication only (Phase 1)
- Target Region: Australia, Currency: AUD, Language: English only

**CORE SYSTEM REQUIREMENTS:**
1. User Management & Authentication
2. Franchise Management
3. Document Management System
4. Sales Script Management
5. Lead Management System
6. Question Bank & Prequalification
7. General Settings & Configuration
8. Meeting Scheduling Integration
9. Analytics & Reporting

**TECHNICAL STACK:**
- Backend: FastAPI (Python)
- Database: PostgreSQL + Vector DB (Pinecone/Weaviate)
- AI/RAG: OpenAI GPT with RAG
- SMS: Twilio/Telstra integration
- Calendar: Calendly integration

Please start by designing the overall system architecture, then proceed with implementation.</textarea>
                    <br><br>
                    <button class="btn" id="start-btn" onclick="startDevelopment()">🚀 Start Development</button>
                    <button class="btn" id="stop-btn" onclick="stopDevelopment()" disabled>🛑 Stop Development</button>
                </div>
            </div>
            
            <div class="chat-area">
                <h2>💬 Agent Communication</h2>
                <div class="messages-container" id="messages">
                    <div class="message system">
                        <div class="agent-name">System</div>
                        <div>Welcome to AutoGen Web GUI! Enter your requirements and click "Start Development" to begin.</div>
                        <div class="timestamp">Ready to start</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const ws = new WebSocket(`ws://${window.location.host}/ws`);
        
        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            
            if (data.type === 'status_update') {
                updateStatus(data.data);
            } else if (data.type === 'agent_message') {
                addMessage(data.data);
            }
        };
        
        function updateStatus(status) {
            document.getElementById('current-phase').textContent = status.current_phase;
            document.getElementById('progress-text').textContent = status.progress + '%';
            document.getElementById('progress-bar').style.width = status.progress + '%';
            document.getElementById('development-status').textContent = status.development_active ? 'Yes' : 'No';
            
            // Update buttons
            document.getElementById('start-btn').disabled = status.development_active;
            document.getElementById('stop-btn').disabled = !status.development_active;
            
            // Update agents status
            const agentsDiv = document.getElementById('agents-status');
            if (Object.keys(status.agents_status).length === 0) {
                agentsDiv.innerHTML = '<p>No agents active</p>';
            } else {
                agentsDiv.innerHTML = Object.entries(status.agents_status)
                    .map(([name, info]) => `<p><strong>${name}:</strong> ${info.status}</p>`)
                    .join('');
            }
        }
        
        function addMessage(message) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${message.message_type}`;
            
            messageDiv.innerHTML = `
                <div class="agent-name">${message.agent_name}</div>
                <div>${message.message.replace(/\n/g, '<br>')}</div>
                <div class="timestamp">${new Date(message.timestamp).toLocaleTimeString()}</div>
            `;
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function startDevelopment() {
            const requirements = document.getElementById('requirements').value;
            ws.send(JSON.stringify({
                type: 'start_development',
                requirements: requirements
            }));
        }
        
        function stopDevelopment() {
            ws.send(JSON.stringify({
                type: 'stop_development'
            }));
        }
    </script>
</body>
</html>'''
    
    # Write the HTML template
    with open("templates/dashboard.html", "w") as f:
        f.write(html_template)
    
    print("🌐 Starting AutoGen Web GUI...")
    print("📱 Open your browser and go to: http://localhost:8080")
    print("🚀 You can now run AutoGen remotely with real-time updates!")
    
    uvicorn.run(app, host="0.0.0.0", port=8080)
