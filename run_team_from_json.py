import asyncio
import os
import json
from autogen_core import ComponentLoader
from autogen_agentchat.ui import Console

async def main():
    """Load and run the AI development team from JSON configuration"""

    # Set up environment variables (replace with your actual values)
    # Set OpenAI API key
    os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"

    print("✅ OpenAI API key configured successfully")

    try:
        # Load the team configuration from JSON file
        with open("ai_dev_team_config.json", "r", encoding="utf-16") as f:
            config_data = json.load(f)

        # Load the team using ComponentLoader
        loader = ComponentLoader()
        team = loader.load_component(config_data)

        # Define the task
        task = """
        Build an AI Enabled Prospect Outreach System (Phase 1) with the following requirements:

        **PROJECT OVERVIEW:**
        - Purpose: AI-enabled prospect outreach system to identify lead intent and provide prequalified leads
        - Communication: SMS-based communication only (Phase 1)
        - Target Region: Australia, Currency: AUD, Language: English only
        - Platform: Web-based admin panel with React JS frontend

        **CORE SYSTEM REQUIREMENTS:**
        1. **User Management & Authentication**
           - Login with Email/Mobile, Remember Me, Forgot Password
           - User Role: Master Admin/System Owner

        2. **Franchise Management**
           - CRUD operations for franchises
           - Fields: Name, Category, Region, Budget, Sub Category
           - Upload franchise brochures, Zoho CRM integration, CSV import

        3. **Document Management System**
           - PDF handling with vector storage
           - RAG implementation for document analysis
           - Map franchises with documents

        4. **Sales Script Management**
           - CRUD operations for scripts
           - Map scripts with specific franchises

        5. **Lead Management System**
           - Import via CSV, Zoho CRM sync, manual entry
           - Display leads with communication history and search

        6. **Question Bank & Prequalification**
           - CRUD operations for questions
           - Mark questions as completed, virtual agent exception handling

        7. **General Settings & Configuration**
           - System messages, holiday messages, agent exceptions

        8. **Meeting Scheduling Integration**
           - Calendly integration with OAuth
           - Automatic booking based on availability

        9. **Analytics & Reporting**
           - SMS count per day, escalation tracking, communication history

        **TECHNICAL INTEGRATIONS:**
        - **Zoho CRM**: Fetch leads, push qualification data and SMS history
        - **SMS Service**: Twilio/Telstra integration with automated sending
        - **AI/RAG**: OpenAI GPT with RAG for document analysis and dynamic responses
        - **Calendar**: Calendly integration for meeting scheduling

        **TECHNICAL STACK:**
        - Frontend: React JS with AdminLTE theme
        - Backend: FastAPI (Python preferred)
        - Database: Vector database (Pinecone/Weaviate) + PostgreSQL
        - Infrastructure: AWS deployment

        **CONSTRAINTS:**
        - Phase 1: SMS only, no voice communication
        - Portrait mode only, no tablet support
        - Manual and unit testing required
        - 5-7 weeks timeline, 306 hours effort

        Please start by designing the overall system architecture, then proceed with implementation focusing on scalable, modular structure with proper error handling and API documentation.
        """

        # Run the team and stream the conversation to console
        print("🚀 Starting AI Development Team...")
        print("=" * 60)

        stream = team.run_stream(task=task)
        await Console(stream)

        print("=" * 60)
        print("✅ Team execution completed!")

    except Exception as e:
        print(f"❌ Error running team: {e}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        print("\nTroubleshooting tips:")
        print("1. Make sure your OPENAI_API_KEY is set correctly")
        print("2. Verify ai_dev_team_config.json exists and is valid")
        print("3. Check that all required dependencies are installed")

if __name__ == "__main__":
    asyncio.run(main())
